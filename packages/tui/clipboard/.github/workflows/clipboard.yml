# Copyright 2021 The golang.design Initiative Authors.
# All rights reserved. Use of this source code is governed
# by a MIT license that can be found in the LICENSE file.
#
# Written by <PERSON><PERSON><PERSON> <changkun.de>

name: clipboard

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  platform_test:
    env:
      DISPLAY: ':0.0'
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        go: ['1.24.x']
    steps:
    - name: Install and run dependencies (xvfb libx11-dev)
      if: ${{ runner.os == 'Linux' }}
      run: |
        sudo apt update
        sudo apt install -y xvfb libx11-dev x11-utils libegl1-mesa-dev libgles2-mesa-dev
        Xvfb :0 -screen 0 1024x768x24 > /dev/null 2>&1 &
        # Wait for Xvfb
        MAX_ATTEMPTS=120 # About 60 seconds
        COUNT=0
        echo -n "Waiting for Xvfb to be ready..."
        while ! xdpyinfo -display "${DISPLAY}" >/dev/null 2>&1; do
          echo -n "."
          sleep 0.50s
          COUNT=$(( COUNT + 1 ))
          if [ "${COUNT}" -ge "${MAX_ATTEMPTS}" ]; then
            echo "  Gave up waiting for X server on ${DISPLAY}"
            exit 1
          fi
        done
        echo "Done - Xvfb is ready!"

    - uses: actions/checkout@v2
    - uses: actions/setup-go@v2
      with:
        stable: 'false'
        go-version: ${{ matrix.go }}

    - name: Build (${{ matrix.go }})
      run: |
        go build -o gclip cmd/gclip/main.go
        go build -o gclip-gui cmd/gclip-gui/main.go

    - name: Run Tests with CGO_ENABLED=1 (${{ matrix.go }})
      if: ${{ runner.os == 'Linux' || runner.os == 'macOS'}}
      run: |
        CGO_ENABLED=1 go test -v -covermode=atomic .

    - name: Run Tests with CGO_ENABLED=0 (${{ matrix.go }})
      if: ${{ runner.os == 'Linux' || runner.os == 'macOS'}}
      run: |
        CGO_ENABLED=0 go test -v -covermode=atomic .

    - name: Run Tests on Windows (${{ matrix.go }})
      if: ${{ runner.os == 'Windows'}}
      run: |
        go test -v -covermode=atomic .