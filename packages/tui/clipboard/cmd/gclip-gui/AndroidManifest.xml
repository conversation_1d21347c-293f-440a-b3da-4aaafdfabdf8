<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The golang.design Initiative Authors.
All rights reserved. Use of this source code is governed
by a MIT license that can be found in the LICENSE file.

Written by <PERSON><PERSON><PERSON> <changkun.de>
-->
<manifest
	xmlns:android="http://schemas.android.com/apk/res/android"
	package="design.golang.clipboard.gclip"
	android:versionCode="1"
	android:versionName="1.0">


	<!-- In order to access the clipboard, the application manifest must
	     specify the permission requirement. See the following page for
	     details.
	     http://developer.android.com/guide/topics/manifest/manifest-intro.html#perms -->
	<uses-permission android:name="android.permission.CLIPBOARD" />

	<application android:label="gclip" android:debuggable="true">
	<activity android:name="org.golang.app.GoNativeActivity"
		android:label="Gclip"
		android:configChanges="orientation|keyboardHidden">
		<meta-data android:name="android.app.lib_name" android:value="Gclip" />
		<intent-filter>
			<action android:name="android.intent.action.MAIN" />
			<category android:name="android.intent.category.LAUNCHER" />
		</intent-filter>
	</activity>
	</application>
</manifest>
