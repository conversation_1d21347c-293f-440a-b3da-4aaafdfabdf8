{"packages": {".": {}}, "$schema": "https://raw.githubusercontent.com/stainless-api/release-please/main/schemas/config.json", "include-v-in-tag": true, "include-component-in-tag": false, "versioning": "prerelease", "prerelease": true, "bump-minor-pre-major": true, "bump-patch-for-minor-pre-major": false, "pull-request-header": "Automated Release PR", "pull-request-title-pattern": "release: ${version}", "changelog-sections": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "revert", "section": "Reverts"}, {"type": "chore", "section": "Chores"}, {"type": "docs", "section": "Documentation"}, {"type": "style", "section": "Styles"}, {"type": "refactor", "section": "Refactors"}, {"type": "test", "section": "Tests", "hidden": true}, {"type": "build", "section": "Build System"}, {"type": "ci", "section": "Continuous Integration", "hidden": true}], "release-type": "go", "extra-files": ["internal/version.go", "README.md"]}