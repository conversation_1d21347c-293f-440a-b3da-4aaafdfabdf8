// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

package opencode

import (
	"github.com/sst/opencode-sdk-go/internal/apierror"
	"github.com/sst/opencode-sdk-go/shared"
)

type Error = apierror.Error

// This is an alias to an internal type.
type ProviderAuthError = shared.ProviderAuthError

// This is an alias to an internal type.
type ProviderAuthErrorData = shared.ProviderAuthErrorData

// This is an alias to an internal type.
type ProviderAuthErrorName = shared.ProviderAuthErrorName

// This is an alias to an internal value.
const ProviderAuthErrorNameProviderAuthError = shared.ProviderAuthErrorNameProviderAuthError

// This is an alias to an internal type.
type UnknownError = shared.UnknownError

// This is an alias to an internal type.
type UnknownErrorData = shared.UnknownErrorData

// This is an alias to an internal type.
type UnknownErrorName = shared.UnknownErrorName

// This is an alias to an internal value.
const UnknownErrorNameUnknownError = shared.UnknownErrorNameUnknownError
