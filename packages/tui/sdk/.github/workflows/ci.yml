name: CI
on:
  push:
    branches-ignore:
      - 'generated'
      - 'codegen/**'
      - 'integrated/**'
      - 'stl-preview-head/**'
      - 'stl-preview-base/**'
  pull_request:
    branches-ignore:
      - 'stl-preview-head/**'
      - 'stl-preview-base/**'

jobs:
  lint:
    timeout-minutes: 10
    name: lint
    runs-on: ${{ github.repository == 'stainless-sdks/opencode-go' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    if: github.event_name == 'push' || github.event.pull_request.head.repo.fork

    steps:
      - uses: actions/checkout@v4

      - name: Setup go
        uses: actions/setup-go@v5
        with:
          go-version-file: ./go.mod

      - name: Run lints
        run: ./scripts/lint
  test:
    timeout-minutes: 10
    name: test
    runs-on: ${{ github.repository == 'stainless-sdks/opencode-go' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    if: github.event_name == 'push' || github.event.pull_request.head.repo.fork
    steps:
      - uses: actions/checkout@v4

      - name: Setup go
        uses: actions/setup-go@v5
        with:
          go-version-file: ./go.mod

      - name: Bootstrap
        run: ./scripts/bootstrap

      - name: Run tests
        run: ./scripts/test
