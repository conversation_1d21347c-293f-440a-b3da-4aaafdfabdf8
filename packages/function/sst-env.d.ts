/* This file is auto-generated by SST. Do not edit. */
/* tslint:disable */
/* eslint-disable */
/* deno-fmt-ignore-file */

import "sst"
declare module "sst" {
  export interface Resource {
    Web: {
      type: "sst.cloudflare.Astro"
      url: string
    }
  }
}
// cloudflare
import * as cloudflare from "@cloudflare/workers-types"
declare module "sst" {
  export interface Resource {
    Api: cloudflare.Service
    Bucket: cloudflare.R2Bucket
  }
}

import "sst"
export {}
