.diff {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--sl-color-divider);
  background-color: var(--sl-color-bg-surface);
  border-radius: 0.25rem;
}

.desktopView {
  display: block;
}

.mobileView {
  display: none;
}

.mobileBlock {
  display: flex;
  flex-direction: column;
}

.row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: stretch;
}

.beforeColumn,
.afterColumn {
  display: flex;
  flex-direction: column;
  overflow-x: visible;
  min-width: 0;
  align-items: stretch;
}

.beforeColumn {
  border-right: 1px solid var(--sl-color-divider);
}

.diff > .row:first-child [data-section="cell"]:first-child {
  padding-top: 0.5rem;
}

.diff > .row:last-child [data-section="cell"]:last-child {
  padding-bottom: 0.5rem;
}

[data-section="cell"] {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: 0.1875rem 0.5rem 0.1875rem 2.2ch;
  margin: 0;

  &[data-display-mobile="true"] {
    display: none;
  }

  pre {
    --shiki-dark-bg: var(--sl-color-bg-surface) !important;
    background-color: var(--sl-color-bg-surface) !important;

    white-space: pre-wrap;
    word-break: break-word;

    code > span:empty::before {
      content: "\00a0";
      white-space: pre;
      display: inline-block;
      width: 0;
    }
  }
}

[data-diff-type="removed"] {
  background-color: var(--sl-color-red-low);

  pre {
    --shiki-dark-bg: var(--sl-color-red-low) !important;
    background-color: var(--sl-color-red-low) !important;
  }

  &::before {
    content: "-";
    position: absolute;
    left: 0.5ch;
    user-select: none;
    color: var(--sl-color-red-high);
  }
}

[data-diff-type="added"] {
  background-color: var(--sl-color-green-low);

  pre {
    --shiki-dark-bg: var(--sl-color-green-low) !important;
    background-color: var(--sl-color-green-low) !important;
  }

  &::before {
    content: "+";
    position: absolute;
    left: 0.6ch;
    user-select: none;
    color: var(--sl-color-green-high);
  }
}

@media (max-width: 40rem) {
  .desktopView {
    display: none;
  }

  .mobileView {
    display: block;
  }
}
