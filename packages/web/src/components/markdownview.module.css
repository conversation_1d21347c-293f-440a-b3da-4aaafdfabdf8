.markdown-body {
  font-size: 0.875rem;
  line-height: 1.5;

  p,
  blockquote,
  ul,
  ol,
  dl,
  table,
  pre {
    margin-bottom: 1rem;
  }

  strong {
    font-weight: 600;
  }

  ol {
    list-style-position: inside;
    padding-left: 0.75rem;
  }
  ul {
    padding-left: 1.5rem;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  & > *:last-child {
    margin-bottom: 0;
  }

  pre {
    white-space: pre-wrap;
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 0, 0, 0.2);
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  code {
    font-weight: 500;

    &:not(pre code) {
      &::before {
        content: "`";
        font-weight: 700;
      }
      &::after {
        content: "`";
        font-weight: 700;
      }
    }
  }
}
