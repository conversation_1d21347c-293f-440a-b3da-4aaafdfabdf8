---
import { Image } from 'astro:assets';
import config from "virtual:starlight/user-config";
import type { Props } from '@astrojs/starlight/props';

import CopyIcon from "../assets/lander/copy.svg";
import CheckIcon from "../assets/lander/check.svg";
import Screenshot from "../assets/lander/screenshot-splash.png";

const { data } = Astro.locals.starlightRoute.entry;
const { title = data.title, tagline, image, actions = [] } = data.hero || {};

const imageAttrs = {
    loading: 'eager' as const,
    decoding: 'async' as const,
    width: 400,
    alt: image?.alt || '',
};

const github = config.social.filter(s => s.icon === 'github')[0];

const command = "curl -fsSL"
const protocol = "https://"
const url = "opencode.ai/install"
const bash = "| bash"

let darkImage: ImageMetadata | undefined;
let lightImage: ImageMetadata | undefined;
let rawHtml: string | undefined;
if (image) {
    if ('file' in image) {
        darkImage = image.file;
    } else if ('dark' in image) {
        darkImage = image.dark;
        lightImage = image.light;
    } else {
        rawHtml = image.html;
    }
}
---
<div class="hero">
    <section class="top">
      <div class="logo">
        <Image
          src={darkImage}
          {...imageAttrs}
          class:list={{ 'light:sl-hidden': Boolean(lightImage) }}
        />
        <Image src={lightImage} {...imageAttrs} class="dark:sl-hidden" />
      </div>
      <h1>The AI coding agent built for the terminal.</h1>
    </section>

    <section class="cta">
      <div class="col1">
        <a href="/docs">Docs</a>
      </div>
      <div class="col2">
      <button class="command" data-command={`${command} ${protocol}${url} ${bash}`}>
          <code>
            <span>{command}&nbsp;</span><span class="protocol">{protocol}</span><span class="highlight">{url}</span>&nbsp;{bash}
          </code>
          <span class="copy">
            <CopyIcon />
            <CheckIcon />
          </span>
        </button>
      </div>
      <div class="col3">
        <a href={github.href}>GitHub</a>
      </div>
    </section>

    <section class="content">
      <ul>
        <li><b>Native TUI</b>: A responsive, native, themeable terminal UI.</li>
        <li><b>LSP enabled</b>: Automatically loads the right LSPs for the LLM.</li>
        <li><b>Multi-session</b>: Start multiple agents in parallel on the same project.</li>
        <li><b>Shareable links</b>: Share a link to any sessions for reference or to debug.</li>
        <li><b>Claude Pro</b>: Log in with Anthropic to use your Claude Pro or Max account.</li>
        <li><b>Use any model</b>: Supports 75+ LLM providers through <a href="https://models.dev">Models.dev</a>, including local models.</li>
      </ul>
    </section>

    <section class="images">
      <div>
        <p>opencode TUI with the tokyonight theme</p>
        <Image width={600} src={Screenshot} alt="opencode TUI with the tokyonight theme" />
      </div>
    </section>

    <section class="footer">
      <div class="col1">
        <span>Version: Beta</span>
      </div>
      <div class="col2">
        <span>Author: <a href="https://sst.dev">SST</a></span>
      </div>
    </section>
</div>

<style>
.hero {
  --padding: 3rem;
  --vertical-padding: 2rem;
  --heading-font-size: var(--sl-text-3xl);

  margin: 1rem;
  border: 2px solid var(--sl-color-border);
}
@media (max-width: 30rem) {
  .hero {
    --padding: 1rem;
    --vertical-padding: 1rem;
    --heading-font-size: var(--sl-text-2xl);

    margin: 0.5rem;
  }
}

section.top {
  padding: var(--padding);

  h1 {
    margin-top: calc(var(--vertical-padding) / 8);
    font-size: var(--heading-font-size);
    line-height: 1.25;
    text-transform: uppercase;
  }

  img {
    height: auto;
    width: clamp(200px, 70vw, 400px);
  }
}

section.cta {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  border-top: 2px solid var(--sl-color-border);

  @media (max-width: 50rem) {
    flex-direction: column;

    & > div.col1 { order: 1; }
    & > div.col3 { order: 2; }
    & > div.col2 { order: 3; }
  }

  & > div {
    line-height: 1.4;
    padding: calc(var(--padding) / 2) 1rem;

    a {
      font-size: 1rem;
    }
  }
  & > div.col1, & > div.col3 {
    flex: 1 1 auto;
    text-align: center;
    text-transform: uppercase;

    @media (max-width: 50rem) {
      padding-bottom: calc(var(--padding) / 2 + 4px);
    }
  }
  & > div.col2 {
    flex: 0 0 auto;
  }

  & > div + div {
    border-left: 2px solid var(--sl-color-border);

    @media (max-width: 50rem) {
      border-left: none;
      border-top: 2px solid var(--sl-color-border);
    }
  }

  .command {
    all: unset;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    justify-content: center;
    cursor: pointer;
    width: 100%;

    code {
      color: var(--sl-color-text-secondary);
      font-size: 1.125rem;

      @media (max-width: 24rem) {
        font-size: 0.875rem;
      }
      @media (max-width: 30rem) {
        span.protocol {
          display: none;
        }
      }
      @media (max-width: 43rem) {
        text-align: center;
        span:first-child {
          display: block;
        }
      }
    }
    code .highlight {
      color: var(--sl-color-text);
      font-weight: 500;
    }

    .copy {
      line-height: 1;
      padding: 0;
      @media (max-width: 43rem) {
        display: none;
      }
    }
    .copy svg {
      width: 1rem;
      height: 1rem;
      vertical-align: middle;
    }
    .copy svg:first-child {
      color: var(--sl-color-text-dimmed);
    }
    .copy svg:last-child {
      color: var(--sl-color-text);
      display: none;
    }
    &.success .copy {
      pointer-events: none;
    }
    &.success .copy svg:first-child {
      display: none;
    }
    &.success .copy svg:last-child {
      display: inline;
    }
  }
}

section.content {
  border-top: 2px solid var(--sl-color-border);
  padding: var(--padding);

  ul {
    padding-left: 1rem;

    li + li {
      margin-top: calc(var(--vertical-padding) / 2);
    }

    li b {
      text-transform: uppercase;
    }
  }
}

section.images {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  border-top: 2px solid var(--sl-color-border);

  & > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: calc(var(--padding) / 4);
    padding: calc(var(--padding) / 2);
    border-width: 0;
    border-style: solid;
    border-color: var(--sl-color-border);

    & > div, p {
      flex: 1;
      display: flex;
      align-items: center;
    }
  }

  p {
    letter-spacing: -0.03125rem;
    text-transform: uppercase;
    color: var(--sl-color-text-dimmed);

    @media (max-width: 30rem) {
      font-size: 0.75rem;
    }
  }

  img {
    align-self: center;
    width: 100%;
    max-width: 600px;
    height: auto;
  }

  & > div + div {
    border-width: 0 0 0 2px;
  }

  @media (max-width: 30rem) {
    & {
      flex-direction: column;
    }
    & > div + div {
      border-width: 2px 0 0 0;
    }
  }
}

section.approach {
  border-top: 2px solid var(--sl-color-border);
  padding: var(--padding);

  p + p {
    margin-top: var(--vertical-padding);
  }
}

section.footer {
  border-top: 2px solid var(--sl-color-border);
  display: flex;
  flex-direction: row;

  & > div {
    flex: 1;
    text-align: center;
    text-transform: uppercase;
    padding: calc(var(--padding) / 2) 0.5rem;
  }

  & > div + div {
    border-left: 2px solid var(--sl-color-border);
  }
}
</style>

<style is:global>
:root[data-has-hero] {
  header.header {
    display: none;
  }
  .main-frame {
    padding-top: 0;

    .main-pane > main {
      padding: 0;
    }
  }
  main > .content-panel .sl-markdown-content {
    margin-top: 0;
  }
}
</style>

<script>
  const button = document.querySelector("button.command") as HTMLButtonElement;

  button?.addEventListener("click", () => {
    navigator.clipboard.writeText(button.dataset.command!);
    button.classList.toggle("success");
    setTimeout(() => {
      button.classList.toggle("success");
    }, 1500);
  });
</script>
