.root {
  border: 1px solid var(--sl-color-blue-high);
  padding: 0.5rem calc(0.5rem + 3px);
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  align-self: flex-start;
  max-width: var(--md-tool-width);

  &[data-highlight="true"] {
    background-color: var(--sl-color-blue-low);
  }

  [data-slot="expand-button"] {
    flex: 0 0 auto;
    padding: 2px 0;
    font-size: 0.75rem;
  }

  [data-slot="markdown"] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    overflow: hidden;

    [data-expanded] & {
      display: block;
    }

    font-size: 0.875rem;
    line-height: 1.5;

    p,
    blockquote,
    ul,
    ol,
    dl,
    table,
    pre {
      margin-bottom: 1rem;
    }

    strong {
      font-weight: 600;
    }

    ol {
      list-style-position: inside;
      padding-left: 0.75rem;
    }

    ul {
      padding-left: 1.5rem;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-size: 0.875rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    & > *:last-child {
      margin-bottom: 0;
    }

    pre {
      --shiki-dark-bg: var(--sl-color-bg-surface) !important;
      background-color: var(--sl-color-bg-surface) !important;
      padding: 0.5rem 0.75rem;
      line-height: 1.6;
      font-size: 0.75rem;
      white-space: pre-wrap;
      word-break: break-word;

      span {
        white-space: break-spaces;
      }
    }

    code {
      font-weight: 500;

      &:not(pre code) {
        &::before {
          content: "`";
          font-weight: 700;
        }

        &::after {
          content: "`";
          font-weight: 700;
        }
      }
    }

    table {
      border-collapse: collapse;
      width: 100%;
    }

    th,
    td {
      border: 1px solid var(--sl-color-border);
      padding: 0.5rem 0.75rem;
      text-align: left;
    }

    th {
      border-bottom: 1px solid var(--sl-color-border);
    }

    /* Remove outer borders */
    table tr:first-child th,
    table tr:first-child td {
      border-top: none;
    }

    table tr:last-child td {
      border-bottom: none;
    }

    table th:first-child,
    table td:first-child {
      border-left: none;
    }

    table th:last-child,
    table td:last-child {
      border-right: none;
    }
  }
}
