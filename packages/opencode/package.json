{"$schema": "https://json.schemastore.org/package.json", "version": "0.0.5", "name": "opencode", "type": "module", "private": true, "scripts": {"typecheck": "tsc --noEmit", "dev": "bun run ./src/index.ts"}, "bin": {"opencode": "./bin/opencode"}, "exports": {"./*": "./src/*.ts"}, "devDependencies": {"@ai-sdk/amazon-bedrock": "2.2.10", "@ai-sdk/anthropic": "1.2.12", "@tsconfig/bun": "1.0.7", "@types/bun": "latest", "@types/turndown": "5.0.5", "@types/yargs": "17.0.33", "typescript": "catalog:", "zod-to-json-schema": "3.24.5"}, "dependencies": {"@clack/prompts": "0.11.0", "@flystorage/file-storage": "1.1.0", "@flystorage/local-fs": "1.1.0", "@hono/zod-validator": "0.5.0", "@openauthjs/openauth": "0.4.3", "@standard-schema/spec": "1.0.0", "ai": "catalog:", "decimal.js": "10.5.0", "diff": "8.0.2", "env-paths": "3.0.0", "hono": "4.7.10", "hono-openapi": "0.4.8", "isomorphic-git": "1.32.1", "open": "10.1.2", "remeda": "2.22.3", "ts-lsp-client": "1.0.3", "turndown": "7.2.0", "vscode-jsonrpc": "8.2.1", "vscode-languageclient": "8", "xdg-basedir": "5.1.0", "yargs": "18.0.0", "zod": "catalog:", "zod-openapi": "4.2.4", "zod-validation-error": "3.5.2"}}