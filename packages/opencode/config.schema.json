{"type": "object", "properties": {"$schema": {"type": "string", "description": "JSON schema reference for configuration validation"}, "theme": {"type": "string", "description": "Theme name to use for the interface"}, "keybinds": {"type": "object", "properties": {"leader": {"type": "string", "default": "ctrl+x", "description": "Leader key for keybind combinations\n\ndefault: `ctrl+x`", "examples": ["ctrl+x"]}, "app_help": {"type": "string", "default": "<leader>h", "description": "Show help dialog\n\ndefault: `<leader>h`", "examples": ["<leader>h"]}, "editor_open": {"type": "string", "default": "<leader>e", "description": "Open external editor\n\ndefault: `<leader>e`", "examples": ["<leader>e"]}, "session_new": {"type": "string", "default": "<leader>n", "description": "Create a new session\n\ndefault: `<leader>n`", "examples": ["<leader>n"]}, "session_list": {"type": "string", "default": "<leader>l", "description": "List all sessions\n\ndefault: `<leader>l`", "examples": ["<leader>l"]}, "session_share": {"type": "string", "default": "<leader>s", "description": "Share current session\n\ndefault: `<leader>s`", "examples": ["<leader>s"]}, "session_unshare": {"type": "string", "default": "<leader>u", "description": "Unshare current session\n\ndefault: `<leader>u`", "examples": ["<leader>u"]}, "session_interrupt": {"type": "string", "default": "esc", "description": "Interrupt current session\n\ndefault: `esc`", "examples": ["esc"]}, "session_compact": {"type": "string", "default": "<leader>c", "description": "Compact the session\n\ndefault: `<leader>c`", "examples": ["<leader>c"]}, "tool_details": {"type": "string", "default": "<leader>d", "description": "Toggle tool details\n\ndefault: `<leader>d`", "examples": ["<leader>d"]}, "model_list": {"type": "string", "default": "<leader>m", "description": "List available models\n\ndefault: `<leader>m`", "examples": ["<leader>m"]}, "theme_list": {"type": "string", "default": "<leader>t", "description": "List available themes\n\ndefault: `<leader>t`", "examples": ["<leader>t"]}, "file_list": {"type": "string", "default": "<leader>f", "description": "List files\n\ndefault: `<leader>f`", "examples": ["<leader>f"]}, "file_close": {"type": "string", "default": "esc", "description": "Close file\n\ndefault: `esc`", "examples": ["esc"]}, "file_search": {"type": "string", "default": "<leader>/", "description": "Search file\n\ndefault: `<leader>/`", "examples": ["<leader>/"]}, "file_diff_toggle": {"type": "string", "default": "<leader>v", "description": "Split/unified diff\n\ndefault: `<leader>v`", "examples": ["<leader>v"]}, "project_init": {"type": "string", "default": "<leader>i", "description": "Create/update AGENTS.md\n\ndefault: `<leader>i`", "examples": ["<leader>i"]}, "input_clear": {"type": "string", "default": "ctrl+c", "description": "Clear input field\n\ndefault: `ctrl+c`", "examples": ["ctrl+c"]}, "input_paste": {"type": "string", "default": "ctrl+v", "description": "Paste from clipboard\n\ndefault: `ctrl+v`", "examples": ["ctrl+v"]}, "input_submit": {"type": "string", "default": "enter", "description": "Submit input\n\ndefault: `enter`", "examples": ["enter"]}, "input_newline": {"type": "string", "default": "shift+enter,ctrl+j", "description": "Insert newline in input\n\ndefault: `shift+enter,ctrl+j`", "examples": ["shift+enter,ctrl+j"]}, "messages_page_up": {"type": "string", "default": "pgup", "description": "Scroll messages up by one page\n\ndefault: `pgup`", "examples": ["pgup"]}, "messages_page_down": {"type": "string", "default": "pgdown", "description": "Scroll messages down by one page\n\ndefault: `pgdown`", "examples": ["pgdown"]}, "messages_half_page_up": {"type": "string", "default": "ctrl+alt+u", "description": "Scroll messages up by half page\n\ndefault: `ctrl+alt+u`", "examples": ["ctrl+alt+u"]}, "messages_half_page_down": {"type": "string", "default": "ctrl+alt+d", "description": "Scroll messages down by half page\n\ndefault: `ctrl+alt+d`", "examples": ["ctrl+alt+d"]}, "messages_previous": {"type": "string", "default": "ctrl+up", "description": "Navigate to previous message\n\ndefault: `ctrl+up`", "examples": ["ctrl+up"]}, "messages_next": {"type": "string", "default": "ctrl+down", "description": "Navigate to next message\n\ndefault: `ctrl+down`", "examples": ["ctrl+down"]}, "messages_first": {"type": "string", "default": "ctrl+g", "description": "Navigate to first message\n\ndefault: `ctrl+g`", "examples": ["ctrl+g"]}, "messages_last": {"type": "string", "default": "ctrl+alt+g", "description": "Navigate to last message\n\ndefault: `ctrl+alt+g`", "examples": ["ctrl+alt+g"]}, "messages_layout_toggle": {"type": "string", "default": "<leader>p", "description": "Toggle layout\n\ndefault: `<leader>p`", "examples": ["<leader>p"]}, "messages_copy": {"type": "string", "default": "<leader>y", "description": "Copy message\n\ndefault: `<leader>y`", "examples": ["<leader>y"]}, "messages_revert": {"type": "string", "default": "<leader>r", "description": "Revert message\n\ndefault: `<leader>r`", "examples": ["<leader>r"]}, "app_exit": {"type": "string", "default": "ctrl+c,<leader>q", "description": "Exit the application\n\ndefault: `ctrl+c,<leader>q`", "examples": ["ctrl+c,<leader>q"]}}, "additionalProperties": false, "description": "Custom keybind configurations"}, "autoshare": {"type": "boolean", "description": "Share newly created sessions automatically"}, "autoupdate": {"type": "boolean", "description": "Automatically update to the latest version"}, "disabled_providers": {"type": "array", "items": {"type": "string"}, "description": "Disable providers that are loaded automatically"}, "model": {"type": "string", "description": "Model to use in the format of provider/model, eg anthropic/claude-2"}, "provider": {"type": "object", "additionalProperties": {"type": "object", "properties": {"api": {"type": "string"}, "name": {"type": "string"}, "env": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "npm": {"type": "string"}, "models": {"type": "object", "additionalProperties": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "release_date": {"type": "string"}, "attachment": {"type": "boolean"}, "reasoning": {"type": "boolean"}, "temperature": {"type": "boolean"}, "tool_call": {"type": "boolean"}, "cost": {"type": "object", "properties": {"input": {"type": "number"}, "output": {"type": "number"}, "cache_read": {"type": "number"}, "cache_write": {"type": "number"}}, "required": ["input", "output"], "additionalProperties": false}, "limit": {"type": "object", "properties": {"context": {"type": "number"}, "output": {"type": "number"}}, "required": ["context", "output"], "additionalProperties": false}, "options": {"type": "object", "additionalProperties": {}}}, "additionalProperties": false}}, "options": {"type": "object", "additionalProperties": {}}}, "required": ["models"], "additionalProperties": false}, "description": "Custom provider configurations and model overrides"}, "mcp": {"type": "object", "additionalProperties": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "local", "description": "Type of MCP server connection"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command and arguments to run the MCP server"}, "environment": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables to set when running the MCP server"}, "enabled": {"type": "boolean", "description": "Enable or disable the MCP server on startup"}}, "required": ["type", "command"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "remote", "description": "Type of MCP server connection"}, "url": {"type": "string", "description": "URL of the remote MCP server"}, "enabled": {"type": "boolean", "description": "Enable or disable the MCP server on startup"}}, "required": ["type", "url"], "additionalProperties": false}]}, "description": "MCP (Model Context Protocol) server configurations"}, "instructions": {"type": "array", "items": {"type": "string"}, "description": "Additional instruction files or patterns to include"}, "experimental": {"type": "object", "properties": {"hook": {"type": "object", "properties": {"file_edited": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "array", "items": {"type": "string"}}, "environment": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["command"], "additionalProperties": false}}}, "session_completed": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "array", "items": {"type": "string"}}, "environment": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["command"], "additionalProperties": false}}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}