# yaml-language-server: $schema=https://app.stainless.com/config-internal.schema.json

organization:
  name: opencode
  docs: "https://opencode.ai/docs"
  contact: "<EMAIL>"

targets:
  typescript:
    package_name: "@opencode-ai/sdk"
    production_repo: "sst/opencode-sdk-js"
    publish:
      npm: true
  go:
    package_name: opencode
    production_repo: sst/opencode-sdk-go
  python:
    project_name: opencode-ai
    package_name: opencode_ai
    production_repo: sst/opencode-sdk-python
    publish:
      pypi: true

environments:
  production: http://localhost:54321

streaming:
  on_event:
    - kind: fallthrough
      handle: yield

resources:
  $shared:
    models:
      unknownError: UnknownError
      providerAuthError: ProviderAuthError
      messageAbortedError: MessageAbortedError

  event:
    methods:
      list:
        endpoint: get /event
        paginated: false
        streaming:
          # This method is always streaming.
          param_discriminator: null

  app:
    models:
      app: App
    methods:
      get: get /app
      init: post /app/init

  find:
    methods:
      text: get /find
      files: get /find/file
      symbols: get /find/symbol

  file:
    methods:
      read: get /file
      status: get /file/status

  config:
    models:
      config: Config
      keybinds: KeybindsConfig
      mcpLocal: McpLocalConfig
      mcpRemote: McpRemoteConfig
      provider: Provider
      model: Model
    methods:
      get: get /config
      providers: get /config/providers

  session:
    models:
      session: Session
      message: Message
      textPart: TextPart
      filePart: FilePart
      toolPart: ToolPart
      stepStartPart: StepStartPart
      assistantMessage: AssistantMessage
      assistantMessagePart: AssistantMessagePart
      userMessage: UserMessage
      userMessagePart: UserMessagePart
      toolStatePending: ToolStatePending
      toolStateRunning: ToolStateRunning
      toolStateCompleted: ToolStateCompleted
      toolStateError: ToolStateError

    methods:
      list: get /session
      create: post /session
      delete: delete /session/{id}
      init: post /session/{id}/init
      abort: post /session/{id}/abort
      share: post /session/{id}/share
      unshare: delete /session/{id}/share
      summarize: post /session/{id}/summarize
      messages: get /session/{id}/message
      chat: post /session/{id}/message

settings:
  disable_mock_tests: true
  license: Apache-2.0

security:
  - {}

readme:
  example_requests:
    default:
      type: request
      endpoint: get /event
      params: {}
    headline:
      type: request
      endpoint: get /event
      params: {}
